# 项目上下文信息

- BusbarCompressionSystem项目性能分析：主要瓶颈在OnReceiveProcessAOI方法的串行处理，包括视觉处理(60-70%)、I/O操作(20-25%)、延时设置(10-15%)。关键优化点：并行工具处理、减少图像转换、异步I/O、调整延时参数。
- BusbarCompressionSystem项目包含完整的MES报工功能，主要通过MES_ORACLE_DATABASE类实现与MES系统的集成，支持设备记录报工、产品绑定、工序校验等功能。报工逻辑集成在生产流程的CHECK1和CHECK2阶段。
- 用户需要查看具体的日志输出示例，分析机器人与视觉系统交互的耗时日志格式和内容。不需要生成总结性文档、测试脚本、编译或运行。
- 相机123频繁断开连接问题：一天发生三四次，导致拍照留底流程失败。系统使用海康威视相机，通过网络连接，缺乏连接状态监控和自动重连机制。
